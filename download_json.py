#!/usr/bin/env python3
"""
下载JSON文件的Python脚本
从指定URL下载JSON文件到当前目录
"""

import requests
import json
import os
from urllib.parse import urlparse

def download_json(url, filename=None):
    """
    从URL下载JSON文件
    
    Args:
        url (str): JSON文件的URL
        filename (str, optional): 保存的文件名，如果不指定则从URL推断
    
    Returns:
        bool: 下载成功返回True，失败返回False
    """
    try:
        print(f"正在下载: {url}")
        
        # 发送GET请求
        response = requests.get(url, timeout=30)
        response.raise_for_status()  # 检查HTTP错误
        
        # 验证是否为有效的JSON
        try:
            json_data = response.json()
        except json.JSONDecodeError:
            print("错误: 下载的内容不是有效的JSON格式")
            return False
        
        # 确定文件名
        if filename is None:
            parsed_url = urlparse(url)
            filename = os.path.basename(parsed_url.path)
            if not filename or not filename.endswith('.json'):
                filename = 'downloaded_data.json'
        
        # 保存到当前目录
        filepath = os.path.join(os.getcwd(), filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        print(f"下载成功! 文件保存为: {filepath}")
        print(f"文件大小: {os.path.getsize(filepath)} 字节")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"网络请求错误: {e}")
        return False
    except IOError as e:
        print(f"文件保存错误: {e}")
        return False
    except Exception as e:
        print(f"未知错误: {e}")
        return False

def main():
    """主函数"""
    url = "https://hxkj.vip/demo/mapData/pcas-code.json"
    
    print("开始下载JSON文件...")
    success = download_json(url, "pcas-code.json")
    
    if success:
        print("下载完成!")
    else:
        print("下载失败!")

if __name__ == "__main__":
    main()
